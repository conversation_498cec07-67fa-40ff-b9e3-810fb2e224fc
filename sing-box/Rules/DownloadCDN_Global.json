{"version": 2, "rules": [{"domain": ["account-api.bandainamcoid.com", "account.bandainamcoid.com", "afdshield-or.download.prss.microsoft.com", "ak.tiles.virtualearth.net", "assets1.xboxlive.com", "assets2.xboxlive.com", "avatars.akamai.steamstatic.com", "avatars.cloudflare.steamstatic.com", "avatars.steamstatic.com", "azuremigrate.download.prss.microsoft.com", "azuremigratetest.download.prss.microsoft.com", "azurestackhub.download.prss.microsoft.com", "azurestackhubuat.download.prss.microsoft.com", "azurestackreleases.download.prss.microsoft.com", "bacon.dyn.riotcdn.net", "bacon.secure.dyn.riotcdn.net", "blizzard.gcdn.cloudn.co.kr", "blzddist1-a.akamaihd.net", "blzddistkr1-a.akamaihd.net", "c.fivem.net", "cbdstest.download.prss.microsoft.com", "cdn.cloudflare.steamstatic.com", "cdn.steamstatic.com", "changelogs-live.fivem.net", "clan.cloudflare.steamstatic.com", "client-patch.gundamevogame.com", "clientpatch.aws.blue-protocol.com", "clientpatch.blue-protocol.com", "cnl-hb-live.fivem.net", "collaborate.download.prss.microsoft.com", "collaborateppe.download.prss.microsoft.com", "community.akamai.steamstatic.com", "community.cloudflare.steamstatic.com", "community.steamstatic.com", "content.cdp.bethesda.net", "content.cfx.re", "contentsync.onenote.com", "cookbook.fivem.net", "crash-ingress.fivem.net", "crashes.fivem.net", "d.docs.live.net", "datastore-main.aws.blue-protocol.com", "db.download.prss.microsoft.com", "dfs.cloudflare.steamstatic.com", "docs-backend.fivem.net", "docs.fivem.net", "download.cdp.bethesda.net", "download.dlsite.com", "download.dm.origin.com", "download.epicgames.com", "download.hikarifield.co.jp", "download2.epicgames.com", "download3.epicgames.com", "download4.epicgames.com", "dreamspark.download.prss.microsoft.com", "dreamsparkuat.download.prss.microsoft.com", "edge.steam-dns.top.comcast.net", "epicgames-download1.akamaized.net", "eu.cdn.blizzard.com", "fastly-download.epicgames.com", "file.chobit.cc", "flg-main.aws.blue-protocol.com", "forum.cfx.re", "forum.fivem.net", "fx-crashingress-1.live.fivem.net", "g-ahpatch-prod.blue-protocol.com", "git.fivem.net", "gog-cdn-fastly-cp77.gog.com", "gog-cdn-fastly.gog.com", "gog-cdn-lumen.secure2.footprint.net", "hierarchyapi.onenote.com", "idms.fivem.net", "img.dlsite.jp", "ipv4.egdownload.fastly-edge.com", "itacademy.download.prss.microsoft.com", "itacademyuat.download.prss.microsoft.com", "keymaster.fivem.net", "kibana-live.fivem.net", "kr.cdn.blizzard.com", "ks-foundation.dyn.riotcdn.net", "ks-foundation.secure.dyn.riotcdn.net", "lambda.fivem.net", "largo.live.fivem.net", "level3.blizzard.com", "level3.cdn.steampipe.steamcontent.com", "lex.download.prss.microsoft.com", "lexuat.download.prss.microsoft.com", "lol.dyn.riotcdn.net", "lol.secure.dyn.riotcdn.net", "masterdata-main.aws.blue-protocol.com", "mbs.download.prss.microsoft.com", "mbsuat.download.prss.microsoft.com", "media.st.dl.eccdnx.com", "media.steampowered.com", "minio-cluster.fivem.net", "mirrors.fivem.net", "mpnbenefits.download.prss.microsoft.com", "mpnbenefitsrtl.download.prss.microsoft.com", "mpnbenefitsrtluat.download.prss.microsoft.com", "msconnect.download.prss.microsoft.com", "msdn.download.prss.microsoft.com", "msdprod-ad.download.prss.microsoft.com", "msproduct.download.prss.microsoft.com", "msproductuat.download.prss.microsoft.com", "myvs.download.prss.microsoft.com", "myvsuat.download.prss.microsoft.com", "native-collector-live.fivem.net", "netdata-internal.fivem.net", "netdata.fivem.net", "object-bnolauncher-ct.bandainamco-ol.jp", "object-bnolauncher-pf.bandainamco-ol.jp", "object-web.blue-protocol.com", "object-web.gundamevolution.jp", "object.gundamevolution.jp", "oemsoc.download.prss.microsoft.com", "oemsocuat.download.prss.microsoft.com", "officecdn.microsoft.com", "officemkt.download.prss.microsoft.com", "officemktuat.download.prss.microsoft.com", "origin-a.akamaihd.net", "osrelease.download.prss.microsoft.com", "pf-cdn-content-prod.azureedge.net", "play.dl.dlsite.com", "policy-live.fivem.net", "public.bn.files.1drv.com", "rethinkdb-admin.fivem.net", "riot-client.dyn.riotcdn.net", "riot-client.secure.dyn.riotcdn.net", "rsm.download.prss.microsoft.com", "rsmuat.download.prss.microsoft.com", "runtime.fivem.net", "sentry.fivem.net", "servers-frontend.fivem.net", "servers-ingress-live.fivem.net", "servers-live.fivem.net", "servers-staging.fivem.net", "servers.fivem.net", "software-static.download.prss.microsoft.com", "software.download.prss.microsoft.com", "ssl-lvlt.cdn.ea.com", "status.fivem.net", "steam.eca.qtlglb.com", "steam.naeu.qtlglb.com", "steam.ru.qtlglb.com", "steamcdn-a.akamaihd.net", "steamcommunity-a.akamaihd.net", "steampipe-kr.akamaized.net", "steampipe-partner.akamaized.net", "steampipe-partner.secure2.footprint.net", "steampipe.akamaized.net", "steamstore-a.akamaihd.net", "steamusercontent-a.akamaihd.net", "steamuserimages-a.akamaihd.net", "storage.fivem.net", "store.cloudflare.steamstatic.com", "storecorefulfillment.download.prss.microsoft.com", "surface.download.prss.microsoft.com", "trial.dlsite.com", "uplaypc-s-ubisoft-ww.cdn.ubi.com", "uplaypc-s-ubisoft.cdn.ubi.com", "upos-hz-mirrorakam.akamaized.net", "us.cdn.blizzard.com", "valorant.dyn.riotcdn.net", "valorant.secure.dyn.riotcdn.net", "video.cloudflare.steamstatic.com", "visualstudio.download.prss.microsoft.com", "vlportal.download.prss.microsoft.com", "volic.download.prss.microsoft.com", "wiki.fivem.net", "windbg.download.prss.microsoft.com", "xvcf1.xboxlive.com", "xvcf2.xboxlive.com"]}, {"domain_suffix": ["cloudflarestorage.com", "hf.co", "storage.live.com"]}, {"domain_keyword": ["epicgames-download"]}]}