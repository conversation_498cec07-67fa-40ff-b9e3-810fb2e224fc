name: |-
  高德地图去广告
desc: |-
  过滤高德地图开屏广告、各页面推广，精简我的页面。
author: |-
  RuCu6[https://github.com/RuCu6], kokoryh[https://github.com/kokoryh]
icon: |-
  https://raw.githubusercontent.com/luestr/IconResource/main/App_icon/120px/Amap.png
openUrl: |-
  https://apps.apple.com/app/id461703208
tag: |-
  去广告
loon_version: |-
  3.2.4(787)
homepage: |-
  https://github.com/luestr/ProxyResource/blob/main/README.md
date: |-
  2025-05-22 21:41:32

rules:
  - AND,((URL-REGEX,"^http:\/\/.+\/amdc\/mobileDispatch"),(USER-AGENT,"AMapiPhone*")),REJECT
  - DOMAIN,amap-aos-info-nogw.amap.com,REJECT
  - DOMAIN,free-aos-cdn-image.amap.com,REJECT
  - DOMAIN-SUFFIX,v.smtcdns.com,REJECT

http:

  mitm:
    - "m5.amap.com"
    - "m5-zb.amap.com"
    - "oss.amap.com"
    - "sns.amap.com"

  url-rewrite:
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/shield\/search\/new_hotword\? - reject-dict
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/faas\/amap-navigation\/card-service-(?:car-end|route-plan) - reject-dict
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/shield\/search_poi\/tips_adv\? - reject-dict
    - >-
      ^https:\/\/oss\.amap\.com\/ws\/banner\/lists\/\? - reject-dict
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/aos\/main\/page\/product\/list\? - reject-dict
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/faas\/amap-navigation\/(?:main-page-assets|main-page-location|ridewalk-end-fc) - reject-dict
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/(?:mapapi\/hint_text\/offline_data|message\/notice\/list|shield\/search\/new_hotword) - reject-dict
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/shield\/scene\/recommend\? - reject-dict
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/valueadded\/weather\/v2\? - reject-dict
    - >-
      ^https:\/\/sns\.amap\.com\/ws\/msgbox\/pull_mp\? - reject-dict
    - >-
      ^https:\/\/m5-zb\.amap\.com\/ws\/boss\/(?:order\/car\/(?:feedback\/get_card_questions|feedback\/viptips|king_toolbox_car_bubble|remark\/satisfactionConf|rights_information)|tips\/onscene_visual_optimization) - reject-dict
    - >-
      ^https:\/\/m5-zb\.amap\.com\/ws\/boss\/pay\/web\/paySuccess\/info\/request - reject-dict

  body-rewrite:
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/shield\/search_business\/process\/marketingOperationStructured\? response-jq delpaths([["data","commonMaterial"]])
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/shield\/search_business\/process\/marketingOperationStructured\? response-jq delpaths([["data","tipsOperationLocation"]])
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/shield\/search_business\/process\/marketingOperationStructured\? response-jq delpaths([["data","resourcePlacement"]])
    - >-
      ^https:\/\/m5\.amap\.com\/ws\/shield\/search_poi\/homepage\? response-jq delpaths([["history_tags"]])
    - >-
      ^https:\/\/m5-zb\.amap\.com\/ws\/sharedtrip\/taxi\/order_detail_car_tips\? response-jq delpaths([["data","carTips","data","popupInfo"]])

  script:
    - match: ^https:\/\/m5\.amap\.com\/ws\/aos\/perception\/publicTravel\/beforeNavi\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/bus\/plan\/integrate\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/c3frontend\/(?:af-(?:hotel|launch)\/page\/main|af-nearby\/nearby)
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/perception\/drive\/(?:routeInfo|routePlan)
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/shield\/search_bff\/hotword\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/shield\/search_poi\/(?:mps|search\/sp|sug|tips_operation_location)
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/faas\/amap-navigation\/(?:card-service-plan-home|main-page)
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/shield\/frogserver\/aocs\/updatable\/1\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/shield\/dsp\/profile\/index\/nodefaasv3\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/shield\/search\/nearbyrec_smart\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5\.amap\.com\/ws\/valueadded\/alimama\/splash_screen\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5-zb\.amap\.com\/ws\/boss\/(?:car\/order\/content_info|order_web\/friendly_information)
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/m5-zb\.amap\.com\/ws\/promotion-web\/resource(\/home)?\?
      name: "移除路线规划推广_33"
      type: response
      require-body: true

    - match: ^https:\/\/(?:info|m5)\.amap\.com\/ws\/shield\/search\/(?:common\/coupon\/info|poi\/detail)
      name: "移除路线规划推广_33"
      type: response
      require-body: true

script-providers:
  "移除路线规划推广_33":
    url: https://kelee.one/Resource/JavaScript/Amap/Amap_remove_ads.js
    interval: 86400

