// ==UserScript==
// @ScriptName        墨鱼去开屏𝐕𝟐.𝟎
// <AUTHOR>
// @TgChannel         𝐡𝐭𝐭𝐩𝐬://𝐭.𝐦𝐞/𝐝𝐝𝐠𝐤𝐬𝐟𝟐𝟎𝟐𝟏
// @TgBot             https://t.me/ddgksf2013_bot
// @WechatID          墨鱼手记
// @UpdateTime        2025-07-19
// @Feedback          💡 请通过邮件反馈问题『其它方式一概无视』：𝐝𝐝𝐠𝐤𝐬𝐟𝟐𝟎𝟏𝟑@𝟏𝟔𝟑.𝐜𝐨𝐦 💡
// @Please            如需引用请注明出处，谢谢合作！
// @Function          去除APP首页启动广告和部分应用内广告，如果有需要的去除开屏广告的APP，可自行在共享表格中添加需求
// @AddRequest        https://bit.ly/addRequestforAdBlock
// @ExtraTxt          Only provide the removal of open-screen advertisements for personally used apps
// @Attention         QuantumultX能去广告，不代表能去所有广告！
// @Mark              名字后面的*代表该应用启动倒计时仍然存在
// @Contributor       @blackmatrix7, @app2smile, @DivineEngine, @kyle, @Nick-workflow, @kkpp, @LE
// @Tips              利用捷径打开URL[填写AppScheme]，即可免开屏广告打开应用，适合无法MITM的APP
// @APPList           无法𝐌𝐈𝐓𝐌的应用：银行类[绝大部分]|抖音|蜂巢|滴答清单|Taio|小米运动|有条下载|Fileball|万年历|豆瓣[信息流]|虎牙直播|货拉拉|番茄小说|携程旅行[APP]|凯叔讲故事|七猫小说|醒图|剪映|潮汐|不背单词|高铁管家|咕咚运动|APTV|DayOne|懂车帝|南京银行|东方航空|中国电信|上汽大众|丰云行|彩云天气[会员]|瑞幸咖啡[抖音小程序]|i茅台|用药助手|东风日产|美图秀秀|星巴克
// @Attention         如果广告仍然存在，请『卸载应用』重新安装，还是不行则表示『规则里没有或已失效』
// @ScriptURL         https://gist.githubusercontent.com/ddgksf2013/12ef6aad209155e7eb62c5b00c11b9dd/raw/StartUpAds.conf
// ==/UserScript==



# ======= ~ ====== #

#以下重写请自行添加，本重写引用不含[喜马拉雅、哔哩哔哩、微博(国际版)、Youtube、Keep、百度贴吧、知乎、高德地图、小红书、网易云、什么值得买、皮皮虾、菜鸟、彩云天气、百度网盘、Reddit、网易邮箱大师]去广告
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/Ximalaya.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/BilibiliAds.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/Weibo.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/YoutubeAds.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/KeepAds.conf
;https://raw.githubusercontent.com/app2smile/rules/refs/heads/master/module/tieba-qx.conf
;https://gist.githubusercontent.com/ddgksf2013/d43179d848586d561dbb968dee93bae8/raw/Zhihu.Adblock.js
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/Amap.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/XiaoHongShuAds.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/NeteaseAds.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/SmzdmAds.conf
;https://gist.githubusercontent.com/ddgksf2013/bb1dadbd32f67c68772caebcc70b0a33/raw/pipixia.adblock.js
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/CaiYunAds.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/CainiaoAds.conf
;https://gist.githubusercontent.com/ddgksf2013/f43026707830c7818ee3ba624e383c8d/raw/baiduCloud.adblock.js
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/RedditAds.conf
;https://raw.githubusercontent.com/ddgksf2013/Rewrite/refs/heads/master/AdBlock/NeteaseMailAds.conf


# ======= 0~9 ======= #

# > Version
^https?:\/\/2025.07.19/c387/v2.0.606 url reject-200
# > 10
^https?:\/\/ftapi\.10jqka\.com\.cn\/futgwapi\/api\/om\/v\d\/ad\/common\/transfer url reject-200
# > 139mail
^https?:\/\/ad\.mcloud\.139\.com\/advertapi url reject-200
^https?:\/\/smsrebuild1\.mail\.10086\.cn\/together\/s url reject-200
# > 369chuxing
^https?:\/\/api\.369cx\.cn\/v\d\/Splash\/GetSplashAd url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/dict.js
# > 58
^https?:\/\/app\.58\.com\/api\/home\/<USER>\/\/[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+){1,4}(:\d+)?\/api\/v\d\/movie\/index_recommend url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/555Ad.js
^https?:\/\/[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+){1,4}(:\d+)?\/.*?\/v\d\/(version$|notice\?|top_notice\?|advert\?position=[^2]+) url reject-200
# > 5eplay
^https?:\/\/.*5eplay\.com\/.*adv_slot url reject-200
# > 12123
^https:\/\/gab\.122\.gov\.cn\/eapp\/m\/sysquery url reject-200
# > 123pan
^https?:\/\/sdk\.1rtb\.net\/sdk\/req_ad url reject-200
^https?:\/\/www\.123pan\.com\/s\/[0-9a-zA-Z=_/-]+\.html url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/123pan.js
# > 36kr
^https?:\/\/gateway\.36kr\.com\/api\/adx\/ad\/show url reject-200
# > 500
^https?:\/\/evs\.500\.com\/esinfo\/loading\/loading url reject-200
# > 78dongman
^https?:\/\/open\.78dm\.net\/v\d\/site\/ad\/ url reject-200
# > 12306
^https?:\/\/ad\.12306\.cn\/ad\/ser\/getAdList url script-analyze-echo-response https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/12306.js


# ======= A ======= #

# > acfun
^https?:\/\/api-new\.app\.acfun\.cn\/rest\/app\/flash\/screen\/ url reject-200
# > aihuishou
^https?:\/\/gw.aihuishou.com\/app-portal\/home\/<USER>\/\/sso.ifanr.com\/jiong\/IOS\/appso\/splash\/ url reject-200
# > alibaba
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.alibaba\.advertisementservice\.getadv\/ url reject-200
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.alibaba\.cbu\.app\.homepage\.startup url reject-200
# > amdc
^http:\/\/amdc\.m\.taobao\.com url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/amdc.js
# > ahfs
^https?:\/\/.*(xbwpys|ahhhhfs)\.com\/($|[0-9a-zA-Z_/]+\/$) url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/ahfs.js
# > aastocks
^https?:\/\/.*aastocks\.com\/ad\/ url reject-200
# > anhuizhangshang10000
^https?:\/\/www\.ahzs10000\.com\/palmhall\/client\/base\/newVerson_getStartUp\.action url reject-200


# ======= B ======= #

# > baiduditu
^https:\/\/newclient\.map\.baidu\.com\/client\/phpui2\/\?qt=ads url script-response-body https://raw.githubusercontent.com/app2smile/rules/master/js/baidumap.js
# > baidutieba
^https?:\/\/tiebac\.baidu\.com\/c\/f\/ad\/getFeedAd url reject-200
# > baidufanyi
^https?:\/\/mime\.baidu\.com\/v\d\/IosStart\/getStartInfo url reject-200
^https?:\/\/mime\.baidu\.com\/v\d\/activity\/advertisement url reject-200
# > baiduwenku
^https?:\/\/appwk\.baidu\.com\/xpage\/interface\/wknaad url reject-200
# > baiduwangpan
^https?:\/\/pan\.baidu\.com\/act\/v\d\/(bchannel|welfare)\/list url reject-200
^https?:\/\/pan\.baidu\.com\/rest\/2\.0\/pcs\/ad url reject-200
^https?:\/\/pan\.baidu\.com\/act\/api\/activityentry url reject-200
# > beijingshouqi
^https?:\/\/gateway\.shouqiev\.com\/fsda\/app\/bootImage\.json url reject-200
# > bishengke
^https?:\/\/res\.pizzahut\.com\.cn\/CRM\/phad\/apphome\/apphome url reject-200
# > beikezhaofang
^https?:\/\/apps\.api\.ke\.com\/config\/config\/(bootpage|getactivityconfig) url reject-200
# > boluobaoqingxiaoshuo
^https:\/\/api\.sfacg\.com\/ioscfg url reject-200
# > bit
^https?:\/\/pan-api\.bitqiu\.com\/activity\/getPromoteGuide url reject-200
# > beijingqiche
^https?:\/\/beijing-gateway-customer\.app-prod\.bjev\.com\.cn\/.*\/openScreenAdvert url reject-200
# > baidushurufa
^https?:\/\/mobads\.baidu\.com\/cpro\/ui\/mads.php url reject-200
# > baobaoshuyunyu
^http:\/\/.*babytree\.com/(plough\.do|go_search\/api\/mobile_search_new\/get_multi_search_default_keywords) url reject-200
^http:\/\/m\.meitun\.com\/newapi\/router\/topic\/hometptf\/feedRecommend url reject-200
# > baicizhan
^https?:\/\/notify\.baicizhan\.com\/rpc\/notify\/get_latest_notify url reject-200
^https?://advertise\.bczeducation\.cn\/rpc\/advertise url reject-200
# > baishitv
^https?:\/\/bp-api\.bestv\.com\.cn\/cms\/api\/free\/open\/advertisingV2 url reject-200
^https?:\/\/bp-api\.bestv\.com\.cn\/cms\/api\/audit\/home\/getCommonMixData url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/master/baishitv.js


# ======= C ======= #

# > clicli
^https?:\/\/js-ad\.ayximgs\.com\.ad-universe-cdn\.hzhcbkj\.cn\/xgapp\.php\/v2\/top_notice url reject-200
# > chelaile
^https?:\/\/web\.chelaile\.net\.cn\/api\/adpub\/ad url reject-200
^https?:\/\/cdn\.\w{3}\.chelaileapp\.cn\/(api\/)?adpub url reject-200
^https?:\/\/api.chelaile.net.cn\/adpub\/ url reject-200
^https?:\/\/api.chelaile.net.cn\/goocity\/advert\/ url reject-200
^https?:\/\/atrace.chelaile.net.cn\/adpub\/ url reject-200
^https?:\/\/atrace.chelaile.net.cn\/exhibit\?&adv_image url reject-200
^https?:\/\/pic1.chelaile.net.cn\/adv\/ url reject-200
^https?:\/\/cdn\.web\.chelaile\.net\.cn\/info-flow\/index\.html url reject-200
# > crunchyroll
https://beta-api.crunchyroll.com/cms url response-body offset_ms":\d+ response-body offset_ms":99999999999999
# > chaoxingxuexitong
^https?:\/\/learn\.chaoxing\.com\/apis\/service\/appConfig url reject-200
# > caifu
^https?:\/\/emdcadvertise\.eastmoney\.com\/infoService\/v\d url reject-200
# > caijingzazhi
^https?:\/\/api\.caijingmobile\.com\/(ad|advert)\/ url reject-200
# > ciweimaoyuedu
^https?:\/\/app\.hbooker\.com\/setting\/get_startpage_url_list url reject-200
# > cainiao
^https?:\/\/cn-acs\.m\.cainiao\.com\/gw\/mtop\.cainiao\.guoguo\.nbnetflow\.ads\.(show|mshow)\.cn\/ url reject-200
^https?:\/\/iyes\.youku\.com\/uts/v\d\/start url reject-200
# > csdn
^https?:\/\/app-gw\.csdn\.net\/silkroad-api\/api\/v\d\/assemble\/list\/pub\/channel\/app_open_screen_ad url reject-200
# > cailianshe
^https?:\/\/api3\.cls\.cn\/v1\/boot\/ad url reject-200
# > ctm
^https?:\/\/www\.ctm\.net\/crm_api\/.*advertise url reject-200
# > caixin
^https?:\/\/gg\.caixin\.com\/s\?z=caixin&op=1&c=3362 url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/caixinads.js
^https?:\/\/pinggai.*caixin\.com\/s\?z=caixin url reject-200


# ======= D ======= #

# > dongkakongjian*
#^https?:\/\/m\.creditcard\.ecitic\.com\/citiccard\/mbk\/appspace-getway\/getWay\/dkkj-system-web\/system\/v\d\/init-config url reject-200
# > dangdang
^https?:\/\/api\.dangdang\.com\/mapi\d\/mobile\/init url reject-200
# > dashixiong
^https?:\/\/sdk\.alibaba\.com\.ailbaba\.me\/.*?\/v\d\/(version|top_notice\?|advert\?position=[^2]+) url reject-200
# > douban
^https?:\/\/api\.douban\.com\/v2\/app_ads\/splash url reject-200
^https?:\/\/api\.douban\.com\/b.*\/common_ads\?.* url reject-200
# > didichuxing
https?://res\.xiaojukeji\.com\/resapi\/activity\/mget url reject-200
https?:\/\/res\.xiaojukeji\.com\/resapi\/activity\/get(Ruled|Preload|PasMultiNotices) url reject-200
# > daoyu
^https?:\/\/daoyu\.sdo\.com\/api\/userCommon\/getAppStartAd url reject-200
# > dongqiudi
^https?:\/\/ap\.dongqiudi\.com\/plat\/v4 url echo-response text/json echo-response https://raw.githubusercontent.com/ddgksf2013/Scripts/master/dongqiudi.js
# > douyu
^https?:\/\/rtbapi\.douyucdn\.cn\/japi\/sign\/app\/getinfo url reject-200
# > dewu
^https:\/\/app\.dewu\.com\/api\/v1\/app\/advertisement\/ url reject-200
# > donghuafeng
^https?:\/\/api\.gamer\.com\.tw\/mobile_app\/anime\/v\d\/anime_get_question\.php url reject-dict
;^https:\/\/api\.gamer\.com\.tw\/mobile_app\/anime\/v\d\/(token|m3u8).php\? url script-response-body https://raw.githubusercontent.com/NobyDa/Script/master/Bahamut/BahamutAnimeAds.js
# > dazhihui
^https?://ssp\.dzh\.com\.cn/v2api/adgroupjson url reject-200
# > dangdangyuedu
^https?:\/\/e.dangdang.com\/media\/api.+\?action=getDeviceStartPage url reject-200
^https?:\/\/api\.dangdang\.com\/mapi\d\/mobile\/init url reject-200
^https?:\/\/mapi\.dangdang\.com\/index\.php\?action=init url reject-200
^https?:\/\/e\.dangdang\.com\/.+?getDeviceStartPage url reject-200
# > dianshijia
^https?:\/\/api\.gaoqingdianshi\.com\/api\/v\d\/ad\/ url reject-200
^https?:\/\/cdn\.dianshihome\.com\/static\/ad\/ url reject-200
# > dingxiangmama
^https?:\/\/mama\.dxy\.com\/api\/cms\/client\/popup-window\/list url reject-200
# > dongfangcaifu
^https?:\/\/[a-z]+\.eastmoney\.com\/infoService\/ url reject-200
^https?:\/\/emdcadvise\.eastmoney\.com\/infoAdviseService\/ url reject-200
# > damai
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.damai\.wireless\.home\.welcome url reject-200
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.damai\.mec\.popup\.get url reject-200
# > dami
^https?:\/\/vip7\.fzwdyy\.cn:8083\/api\/(getAdvertInfo|getGOOGAdvert) url reject-200
# > dingxiangyuan
^https?:\/\/dq\.dxy\.cn\/api\.php\?action=getpostbanners url reject-200
# > dedao
^https?:\/\/entree-ws\.igetget\.com\/oms\/front\/start\/push url reject-200
# > dingdongmaicai
^https?:\/\/maicai\.api\.ddxq\.mobi\/advert\/getAd url response-body rt_time":\d{2} response-body rt_time":40
^https?:\/\/maicai\.api\.ddxq\.mobi\/advert\/startUpScreen url reject-200
# > dejian
^https?:\/\/dj\.palmestore\.com\/zybk\/api\/ad url reject-200
^https?:\/\/saad\.ms\.zhangyue\.net\/ad\/ url reject-200
# > didachuxing
^https?:\/\/capis.*didapinche\.com\/ad\/cx\/startup url reject-200
# > dongkakongjian*
^https?:\/\/ump\.sz\.creditcard\.ecitic\.com\/citiccard\/cm-ump\/ump-gateway\/ump-net-app\/ump-net-app\/adv url reject-200
# > duxiaoman
^https?:\/\/app\.duxiaoman\.com\/walletapp\/misc\/app_startup url reject-200


# ======= E ======= #

# > elema
^https?:\/\/elemecdn.com\/.+\/sitemap url reject-200
^https?:\/\/fuss10.elemecdn.com\/.+\/w\/640\/h\/\d{3,4} url reject-200
^https?:\/\/fuss10.elemecdn.com\/.+\/w\/750\/h\/\d{3,4} url reject-200
^https?:\/\/fuss10.elemecdn.com\/.+.mp4 url reject-200
^https?:\/\/www1.elecfans.com\/www\/delivery\/ url reject-200
# > echongdian
^https?:\/\/cdn-evone-ceph\.echargenet\.com\/gw-emas-cdn\/63c4e3b558bb610008969f89 url reject-200
# > echongdian
^https?:\/\/cdn-evone-ceph\.echargenet\.com\/gw-emas-cdn url reject-200


# ======= F ======= #

# > fandengdushu
^https?:\/\/gateway-api\.dushu365\.com\/chief-orch\/config\/config\/v100\/appConfig url reject-200
# > feizhu
^https:\/\/acs\.m\.taobao\.com\/gw\/mtop\.fliggy\.crm\.screen\.allresource url reject-200
# > feikechaguan
^https?:\/\/.*flyert.*\/api\/mobile\/index\.php\?module=advis url reject-200
^https?:\/\/.*flyert.*\/source\/plugin\/mobile\/mobile\.php\?module=advis url reject-200
^https?:\/\/.*flyert.*\/api\/mobile\/index\.php\?version=\d&mobile=yes&module=basicdata&type=forumlist url response-body adv response-body ddgksf2013
^https?:\/\/.*flyert.*\/source\/plugin\/mobile\/mobile\.php\?module=threadpost&.+?&page=1 url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/fly.js
# > fengyunxing
^https?:\/\/carapp\.gtmc\.com\.cn\/api\/appgtmc\/homePage\/HomePageAction\/queryHomePageImg\.json url reject-200
# > feimaoguanjia
^https?:\/\/app\.flymodem\.com\.cn\/Appapi\/Public\/welecome url reject-200
# > etnet
^http:\/\/gateway2\.etnet\.com\.hk\/etnetApp\/theme\/seasonal\/v30\/theme.json url reject-200
# > fenbi
^https?:\/\/.*fenbi\.com\/(activity\/app\/launcher|app\/iphone\/nxs\/popup) url reject-200
# > futuniuniu
^https?:\/\/api\.futunn\.com\/(v\d\/)?ad\/ url reject-200
# > fenwandao
^https?:\/\/api\.livelab\.com\.cn\/pgc\/advert url reject-200


# ======= G ======= #

# > gongshijilu
^https:\/\/mi\.gdt\.qq\.com\/gdt_mview\.fcg url reject-200
^https:\/\/open\.e\.kuaishou\.com\/rest\/e\/v3\/open\/univ url reject-200
# > gaidepaihang
https://zone.guiderank-app.com/guiderank-web/app/ad/listLaunchADByCity.do url reject-200
# > guanyu
https://guanyu.longfor.com/app-server/api/v1/main/start url reject-200
# > guangqibentian
^https?:\/\/gha\.ghac\.cn\:8081\/base\/app\/api\/ad\/query\?adType url reject-200
# > guojiadili
^https?:\/\/dili\.bdatu\.com\/jiekou\/ad url reject-200
^https?:\/\/wap\.ngchina\.cn\/news\/adverts url reject-200
# > guojiayibaofuwupingtai
^https?:\/\/fuwu\.nhsa\.gov\.cn\/ebus\/fuwu\/api\/base\/cms\/iep\/web\/cms\/hmpgcfg\/queryAppHmpgCfgByApp url reject-200


# ======= H ======= #

# > haoxingtizhongcheng
^http:\/\/open\.fitdays\.cn\/uploads\/ad\/ url reject-200
# > huazhu
^https?:\/\/appapi\.huazhu\.com:\d{4}\/client\/app\/getAppStartPage\/ url reject-200
# > hupu
^https?:\/\/i\d\.hoopchina\.com\.cn/blogfile\//d+\//d+\/BbsImg\.(?<=(big.(png|jpg)))$ url reject-img
^https?:\/\/games\.mobileapi\.hupu\.com\/.+\/(search|interfaceAdMonitor|status|hupuBbsPm)/(hotkey|init|hupuBbsPm)\. url reject-img
^https?:\/\/games\.mobileapi\.hupu\.com\/interfaceAdMonitor url reject-img
^https?:\/\/goblin\.hupu\.com\/.+\/interfaceAd\/getOther url reject-200
^https?:\/\/i1\.hoopchina\.com\.cn\/blogfile\/.+_\d{3}x\d{4} url reject-img
# > hongbanbao
https://fbchina.flipchina.cn/v2/ad/query/* url reject-200
# > huyazhibo
^https?:\/\/business\.msstatic\.com\/advertiser\/material url reject-200
# > haoqixinribao
^https?:\/\/app3\.qdaily\.com\/app\d\/boot_advertisements\.json url reject-200
# > huxiu
^https:\/\/api-ad-product\.huxiu\.com\/Api\/Product\/SDK\/Advert\/Query\/queryAdvertListInfo url reject-200
# > huangyouxiangji
^https?:\/\/api4\.bybutter\.com\/v\d\/app\/placements\/\d\/advertisements url reject-200
# > hanglvzongheng
^https?:\/\/(discardrp|startup)\.umetrip\.com\/gateway\/api\/umetrip\/native url reject-200
^https?:\/\/.*umetrip\.com\.cn\/gateway\/api\/umetrip\/native$ url script-response-header https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/hanglvzongheng.js
;^https?:\/\/114\.115\.217\.129\/gateway\/api\/umetrip\/native$ url script-response-header https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/hanglvzongheng.js
# > hangzhougongjiao
^https?:\/\/m.ibuscloud.com\/v2\/app\/getStartPage url reject-200
# > haohaozhu
^https?:\/\/api\.haohaozhu\.cn\/index\.php\/home\/<USER>\/getStartPhoto url reject-200
# > huabaozhitou
^https?:\/\/api\.touker\.com\/v2\/IAdvertisementAPI\.queryStartAdvertisement url reject-200
# > hema
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.wdk\.render\.querysinglepage url reject-200
^https?:\/\/acs-m\.freshippo\.com\/gw\/mtop\.wdk\.render\.querysinglepage url reject-200
# > hefengtianqi
^https?:\/\/hfapp-service\.qweather\.net\/.*\/ad\/ url reject-200
# > haoqing
^https?:\/\/restapi\.iyunmai\.com\/ad-api\/ url reject-200
# > huaerjiejianwen
^https?:\/\/api-one-wscn\.awtmt\.com\/apiv\d\/advertising\/ads url reject-200
# > hanxiaoquan
^https?:\/\/hxqapi\.hiyun\.tv\/api\/notification\/plans url reject-200
# > hpoi
^https?:\/\/www\.hpoi\.net\.cn\/api\/item\/startPage url reject-200
# > haique
^https?:\/\/cq11344-app-https-api-1\.ictun\.com:443\/api\/app\/v\d\/appad url reject-200
# > huatuzaixian
^https?:\/\/yyfapi\.huatu\.com\/common\/ad\/ url reject-200
# > huitongcaijing
^https?:\/\/data\.hgold\.cn\/ky\/.*\/ad\.php url reject-200


# ======= I ======= #

# > i4
^https?:\/\/list-app-m\.i4\.cn\/getopfstadinfo\.xhtml url reject-200
# > i3
#^https?:\/\/www\.i3zh\.com url response-body cm-pop-up-banners response-body ddgksf2013
# > iQiYi
^https?:\/\/.*cupid\.iqiyi\.com\/mixer\? url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/iqiyi_open_ads.js
# > iHour
^https?:\/\/app-cdn\.2q10\.com\/app\/\w+\/honored url reject-200
# > igpsport
^https?:\/\/prod\.zh\.igpsport\.com\/.+\/GetStartAd url reject-200
# > ithome
^https?:\/\/napi\.ithome\.com\/api\/news\/indexv\d\/iphone url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/ithome.js


# ======= J ======= #

# > jingdongdushu
^https?:\/\/jdread-api\.jd\.com\/jdread\/api\/channel\/module\/opens url reject-200
^https?:\/\/jdread-api\.jd\.com\/jdread\/api\/popup url reject-200
# > jingxi
^https?:\/\/api\.m\.jd\.com\/api\?functionId=delivery_show url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/startup.js
# > jingdong[invalid]
^https?:\/\/api\.m\.jd\.com\/client\.action\?functionId=(start|queryMaterialAdverts) url reject-200
^https?:\/\/(bdsp-x|dsp-x)\.jd\.com\/adx\/ url reject-200
^https?:\/\/api\.m\.jd\.com\/client\.action\?functionId=(hotWords|hotSearchTerms) url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/jd_json.js
# > jingdongjinrong
^https?:\/\/ms\.jr\.jd\.com\/gw\/generic\/aladdin\/(new)?na\/m\/getLoadingPicture url reject-200
# > jingdonglite
^https?:\/\/api\.m\.jd\.com\/client\.action\?functionId=lite_advertising url response-body jdLiteAdvertisingVO response-body ddgksf2013
^https?:\/\/api\.m\.jd\.com\/client\.action\?functionId=lite_SmartPush url response-body pushData response-body ddgksf2013
# > jingdongyun
^https:\/\/router-app-api\.jdcloud\.com\/v\d\/board\/routerAppSplash url reject-200
# > jingdongkuaidi
^https?:\/\/lop-proxy\.jd\.com\/queryAppHomePageMarketingRecommendRuleConfigInfo url reject-200
# > jianxun
^https?:\/\/api\.tipsoon\.com\/api\/v\d\/top\/ad url reject-200
# > jiaohangmaidanba
^https?:\/\/creditcardapp\.bankcomm\.com\/mapp\/common\/(queryGuidePageAds|getPopAds)\.do$ url reject-200
# > jiemianxinwen
^https?:\/\/img\.jiemian\.com\/ads\/ url reject-200
# > jinshi
^https?:\/\/jad-api\.jin10\.com\/ad url reject-200
# > jijianhuilv
^https?:\/\/explorer\.tratao\.com\/api\/client\/v4\/xtransfer\/ad\/ url reject-200
# > jiaxiaoediantong
^https?:\/\/api\.jxedt\.com\/ad\/ url reject-200
# > jiakaobaodian
^https?:\/\/\w+\.kakamobi\.cn\/api\/open\/v\d\/advert-sdk\/ url reject-200
# > jianhanglife
^https?:\/\/yunbusiness\.ccb\.com\/clp_service\/txCtrl\?txcode=A3341A00(2|9) url reject-200
# > jimi
^https?:\/\/superapp\.xgimi\.com\/api/v1\/app\/ad\/configs\?_sort=createdAt:Adesc url reject-200
# > jike
;^https?:\/\/mgw\.mpaas\.cn-hangzhou\.aliyuncs\.com\/mgw\.htm url reject-200
# > jiansheyinhang
^https?:\/\/adv\.ccb\.com\/ebda\/ctm_adv url reject-200
# > jump
^https?:\/\/switch\.jumpvg\.com\/jump\/(getlaunchad|recommend\/ad_conf) url reject-200
# > jingdongxiaojia
^https?:\/\/api\.smart\.jd\.com\/c\/service\/getLoadingLinks url reject-200
# > jiankang160
^https?:\/\/patientgate\.91160\.com\/advert-api\/v\d\/advert url reject-200


# ======= K ======= #

# > kfc
^https?:\/\/res.kfc.com.cn\/advertisement\/ url reject-200
^https?:\/\/res.kfc.com.cn\/CRM\/kfcad\/apphome6\/apphome.*json url response-body bootStrapAd response-body ddgksf2013
https://dynamicad.kfc.com.cn/api/app5/homepage/ai/popup url reject-200
# > kuaikan
^https?:\/\/api.kkmh.com\/v\d\/(ad|advertisement)\/ url reject-200
# > ku'an
^https?:\/\/api\.coolapk\.com\/v6\/(feed\/(replyList|detail)|main\/indexV8|dataList) url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/coolapk.js
^https?:\/\/api.coolapk.com\/v6\/main\/init url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/coolapk.js
^https?:\/\/api\.coolapk\.com\/v6\/search\?.*type=hotSearch url reject-dict
# > kanlixiang
^https?:\/\/api\.vistopia\.com\.cn\/api\/v\d\/home\/advertisement url reject
# > kantianxia
https://open3.vistastory.com/v3/api/index/loading_ad url reject-200
https://open3.vistastory.com/v3/api.*get_popup url reject-200
# > kuake
^https?:\/\/open-cms-api\.(uc|quark)\.cn\/open-cms url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/master/quark.js
# > kuaishou
^https:\/\/open\.e\.kuaishou\.com\/rest\/e\/v3\/open\/univ url reject-200
# > kudi
^https?:\/\/gateway\.abite\.com\/cotti-capi\/customer\/position\/list\?code=cotti-launch-window url reject-200
^https?:\/\/gateway\.cotticoffee\.com\/cotti-capi\/customer\/position\/list\?code=cotti-launch-window url reject-200
# > keep
^https?:\/\/hc-ssp\.sm\.cn url reject-200
# > kuaiying
^https?:\/\/api\.kmovie\.gifshow\.com\/rest\/n\/kmovie\/app\/(resource|banner) url reject-200
# > kujiale
^https?:\/\/www\.kujiale\.com\/app\/queryOpenPage url reject-200
# > kugou
^http:\/\/ads.?s?ervice(retry)?\.(kugou|kglink).(com|cn)\/v\d\/ url reject-200
# > kujiequ
^https?:\/\/api\.kurobbs\.com\/config\/getOpenScreen url reject-200
# > koufujiantou
^https?:\/\/app\.jiantou8\.com\/Home\/APP\/allAd url reject-200
# > kuaidui
^https?:\/\/adx\.zuoyebang\.com\/adxserver\/ url reject-200


# ======= L ======= #

# > lingclub
^https?:\/\/api\.00bang\.cn\/llb\/baseinfo\/advertise\/getAdvertiseByPageCode url reject-200
# > linxi
^https?:\/\/api\.internetofcity\.cn\/api\/resource\/anon\/popups\/(getSplashList|getList) url reject-200
# > lecheng
https://dl-cu-hz.lechange.cn/oms-online/advertisementPush/* url reject-200
# > lanjie100
https://tagit.hyhuo.com/recover/list url reject-200
# > laiChon
^https?:\/\/(mobile|shop)\.laichon\.com\/api\/(v1\/goods\/goodsList|exposureAdvStatistics|getWebAdvList) url reject-200
# > liangBulU
# host-suffix, admobile.top, reject-200
https?:\/\/helper\.2bulu\.com\/(greenPea\/queryTasks|proSpecial\/allData|saveSplashFrequencyStatistics|getPopInfo|getAppEntranceConfig|promote\/list|getSplash|outing\/reqFoundNewList|outing\/reqIndex) url reject-200
# > lanRenTingShu
^https?:\/\/.*\/yyting\/advertclient\/ClientAdvertList.action url reject-200
# > lanjiyin
^https?:\/\/tk\.lanjiyin\.com\.cn\/ad\/getAdList url reject-200
# > leisutiyu
^https?:\/\/app-gateway\.leisuapi\.com\/v\d\/app\/mobile\/(banners|ads) url reject-200
# > liulishuo
^https?:\/\/rengine-platform\.llsapp\.com\/auth\/api\/remoteResource\/darwin url reject-200
# > lingpao
^https?:\/\/apptec\.leapmotor\.cn\/appNewInterface\/getAppStartPageInfo url reject-200
# >lianmeng
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.alimama\.etao\.config\.query url reject-200
# > lofter
^https?:\/\/ad\.lofter\.com\/.*yitou\/madr url reject-200
# > lanrentingshu
^https?:\/\/.*mting\.info\/advert\/ClientAdvertList\.action url reject-200
# > lecheng
^https?:\/\/adx.*anythinktech\.com\/bid url reject-200
# > leke
^https?:\/\/lens\.leoao\.com\/lens\/.+(getUserScheme|queryAppBanners|Advert|popup) url reject-200


# ======= M ======= #

# > MeiTuan
^https?:\/\/peisongapi\.meituan\.com\/client\/getInitiateImage url reject-200
^https?:\/\/img\.meituan\.net\/(adunion|display|midas)\/\w+\.(gif|jpg|jpg\.webp)$ url reject-200
^https?:\/\/(s3plus|flowplus)\.meituan\.net\/v\d\/\w+\/linglong\/\w+\.(gif|jpg|mp4) url reject-200
^https?:\/\/p\d\.meituan\.net\/(bizad|wmbanner)\/\w+\.jpg url reject-200
^https?:\/\/p\d\.meituan\.net\/movie\/\w+\.jpg\?may_covertWebp url reject-200
# > MeiTuanWaiMai
^https?:\/\/img\.meituan\.net\/(bizad|brandCpt)\/\w+\.(png|jpg) url reject-200
^https?:\/\/.*\.meituan\.com\/api\/v\d\/(openscreen\?ad|appstatus\?ad|loadInfo\?|startpicture) url reject-200
^http:\/\/s3plus\.meituan\.net\/.*\/brandcpt-vedio\/.*\?time url reject-200
# > MeiWeiBuYongDeng
^https?:\/\/capi.mwee.cn\/app-api\/V12\/app\/getstartad url reject-200
# > MaFengWo
^https://mapi.mafengwo.cn\/ad\/get_launch_ad_list\/v2 url reject-200
# > manmanmai
^https?:\/\/apapia-sqk\.manmanbuy\.com\/index_json\.ashx url response-body splashAD response-body ddgksf2013
# > MaiDuiDui
^https?:\/\/mob\.mddcloud\.com\.cn\/adApi\/advert url reject-200
# > manhuaren
^https?:\/\/.*mangaapi\.manhuaren\.\w{2,4}\/v\d\/public\/(getStartUpMessage|getStartPageAds|getShelfActivity) url reject-200
^https?:\/\/.*mangaapi\.manhuaren\.\w{2,4}\/v\d\/ad url reject-200
# > MIX
^https?:\/\/dispatcher\.camera360\.com\/api\/v1\/list$ url reject-200
^https?:\/\/mix-api\.camera360\.com\/v\d\/operational-positions url reject-200
# > MaKaLong
^https:\/\/app\.api\.versa-ai\.com\/launch\/ads\? url reject-200
# > MoMo
^https?:\/\/open\.taou\.com\/maimai\/launch_ad url reject-200
^https?:\/\/api\.taou\.com\/sdk\/global\/splash_ad url reject-200
^https?:\/\/track\.mm\.taou\.com/v\d\/track url reject-200
# > maidanba
^https?:\/\/creditcardapp\.bankcomm\.cn\/mappweb_interface\/common\/(qryPopAds|qryLaunchAds)\.do url reject-200
# > mixuebingcheng
^https:\/\/mxsa\.mxbc\.net\/api\/v1\/adinfo\/limitedAds$ url reject-200
# > maoyan*
^https?:\/\/p0\.pipi\.cn\/adAdmin\/\w+.jpg\? url reject-img
# > miyoushe
^https?:\/\/bbs-api\.miyoushe\.com\/apihub\/api\/getAppSplash url reject-200
# > meiyou
^https?:\/\/axxd\.xmseeyouyima\.com\/v\d\/getad url reject-200
^https?:\/\/ad\.seeyouyima\.com url reject-200
# > miguyinyue
^https?:\/\/app\.c\.nf\.migu\.cn\/.*column\/start(-)?up-pic url reject-200
# > manhuashe
^https?:\/\/comicapi\.manhuashe\.com\/v\d\/(ads\/adstrategys|public\/startupactivity) url reject-200
# > maidanglao
^https?:\/\/api\.mcd\.cn\/bff\/portal\/(richpop|home\/splash) url reject-200
# > merit
^https?:\/\/api\.merach\.com\/app\/AppAdvertisingController\/getAdvert url reject-200
# > meisiyixue*
^https?:\/\/app-api\.medsci\.cn\/app-advertisement-space\/showAdList url reject-200
# > manbuzhe
^https?:\/\/admin-app\.edifier\.com\/edifier_provider\/public\/getAppMarqueeForApp url reject-200
# > moomoo
^https?:\/\/api\.moomoo\.com(\/v\d)?\/ad url reject-200
# > miaojie
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.alibaba\.mos\.app\.homepage\.launch url reject-200
# > migushipinaikan
^https?:\/\/aikanvod\.miguvideo\.com\/video\/p\/i_adverseInterface\.jsp url reject-200
# > mijia
^https?:\/\/home\.mi\.com\/cgi-op\/api\/v\d\/recommendation url reject-200
# > maidanba
^https?:\/\/creditcardapp\.bankcomm\.com\/mappweb.*(Advert|Ads).do url reject-200


# ======= N ======= #

# > nanfanghangkong
^https?:\/\/3g\.csair\.com\/CSMBP\/bookProcess\/homepopup\/queryAdvertisement url reject-200
# > nantaihuhao
^https?:\/\/.*zhijianmedia\.cn\/streamAd\/ad\/getAd\?position=SplashScreen url reject-200
^https?:\/\/.*zhijianmedia\.cn\/streamAd\/ad\/getAd\?position=HomepageNewsPosition url reject
# > nbzhuhsou
^https?:\/\/.*ziben\.com\/api\/.*\/adverts url reject
# > nongyeyinhang
^https?:\/\/mobilepaas\.abchina\.com\.cn:441\/mgw\.htm url script-response-header https://raw.githubusercontent.com/ddgksf2013/Scripts/master/abchina.js


# ======= O ======= #

# > omofun
^https?:\/\/103\.91\.210\.141\:2515\/xgapp\.php\/v2\/top_notice url reject-200
# > One
^http:\/\/app\.api\.d3yuiw4\.com\/api\/app\/ad url reject-200
# > Oray
^https?:\/\/slapi\.oray\.net\/client\/ad url reject-200
^https?:\/\/slapi\.oray\.net\/adver url reject-200
# > one
^https?:\/\/api\.21ec74\.com\/v2\.5\/ad url reject-200
# > ocleancare*
^https?:\/\/careapi\.oclean\.com\/mall\/v\d\/User\/GetUserCenter url response-body banner" response-body ddgksf2013"
^https?:\/\/careapi\.oclean\.com\/mall\/v\d\/Temporary\/SafetyGetStartAdvert url reject-200


# ======= P ======= #

# > pikpak
https://access.mypikpak.com/access_controller/v1/area_accessible url reject-200
# > pinduoduo
^https?:\/\/api\.(pinduoduo|yangkeduo)\.com\/api\/cappuccino\/splash url reject-200
# > PiaoGen
^https?:\/\/pss\.txffp\.com\/piaogen\/images\/launchScreen/ url reject-200
# > pengpainews
^https?:\/\/adpai\.thepaper\.cn\/.+&ad= url reject-200
# > pufayinhang
^https?:\/\/lban\.spdb\.com\.cn\/mspmk-web-component\/(getAdvList|prefetchAdvList)\.ah url reject-200
# > pupumarket
^https?:\/\/j1\.pupuapi\.com\/client\/marketing\/advertisement url reject-200
^https?:\/\/j1\.pupuapi\.com\/client\/marketing\/banner\/v\d\?position url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/master/pupumarket.js
# > pica
^https?:\/\/.*tipatipa\.xyz\/announcements url reject-200
# > pansearch
^https?:\/\/www\.pansearch\.me\/api\/adsite url reject-dict
# > pinganzhengquan
^https?:\/\/m\.stock\.pingan\.com\/restapi\/rmd\/open\/O\/api\/openAd url reject-200
# > pinganjiankang
^https?:\/\/api\.jk\.cn\/m\.api url script-response-header https://raw.githubusercontent.com/ddgksf2013/Scripts/master/pinganads.js
# > paojieling
^https?:\/\/pjlapi\.paojd\.cn\/na\/pjlNa url reject-200


# ======= Q ======= #

# > qqbrower
^https:\/\/us\.l\.qq\.com\/exapp\?spsa=\d url reject-200
# > QuNaEr
https://homefront.qunar.com/front/splash/ad url reject-200
^https?:\/\/client\.qunar\.com\/pitcher-proxy\?qrt=p_splashAd url reject-200
# > QiCheZhiJia
^https?:\/\/adproxy.autohome.com.cn\/AdvertiseService\/ url reject-200
^https?:\/\/app2.autoimg.cn\/appdfs\/ url reject-200
# > QiDianDuShu
^https?:\/\/magev\d\.if\.qidian\.com\/argus\/api\/v\d\/client\/getsplashscreen url reject-200
# > qinlingkaimen
^https?:\/\/qadx\.qinlinad\.com\/ad\/ url reject-200
# > qijunjiekancaijin
^https?:\/\/app\.api\.qjjfin\.com\/publicize\/allList url reject-200
# > qqyuedu
^https?:\/\/(commontgw|comapi)\.reader\.qq\.com\/(common\/adV|com-ad\/config) url reject-200
# > qinbaobao
^https?:\/\/api\.qbb6\.com\/ad\/ url reject-200
# > picooc
^https?:\/\/api2\.picooc\.com\/v\d\/api\/focus\/strategy\/execute url reject-200
# > qidianyuedu
^https?:\/\/apps\.teamair\.cn\/app\/version url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/qidian.js
# > qingdaoditie
^https?:\/\/api\.qd-metro\.com\/ngstatic\/appScreenAds url reject-200



# ======= R ======= #

# > RenMinRiBao
^https:\/\/app\.peopleapp\.com\/Api\/\d+/HomeApi\/(adv|getAdvertImage) url reject-200
^https?:\/\/stat\.peopleapp\.com\/ url reject-200
# > renren
^https?:\/\/.*dcloud\.net\.cn\/(app\/acs|uad) url reject-200
# > richanzhilian
^https?:\/\/.*dongfeng-nissan\.com\.cn\/mb-gw\/vmsp-discover\/rest\/business-service\/v\d\/adver url reject-200
# > ruixinkafei
^https?:\/\/capi\.lkcoffee\.com\/resource\/m\/sys\/(homePage\/contactor\/modules|app\/adposNew) url reject-200
# > ruiboshi
^https?:\/\/sdk\.tianmu\.mobi\/ad url reject-200


# ======= S ======= #

# > shaoshupai
https://ios.sspai.com/api/v3/recommend/page/get\?ad.*ios_home_modal url reject-200
# > ShunFengYouXuan
^https://mapi.sfbest.com\/brokerservice-server\/cms\/getPositionById.* url reject-200
# > spotify pro
;^https:\/\/spclient\.wg\.spotify\.com\/(bootstrap\/v1\/bootstrap|user-customization-service\/v1\/customize)$ url script-response-body https://raw.githubusercontent.com/app2smile/rules/master/js/spotify-proto.js
;^https:\/\/spclient\.wg\.spotify\.com\/(artistview\/v1\/artist|album-entity-view\/v2\/album)\/ url script-request-header https://raw.githubusercontent.com/app2smile/rules/master/js/spotify-json.js
# > spotify
^https?://spclient.wg.spotify.com/(ad-logic|ads|.+ad_slot|.+banners|.+canvases|.+cards|.+crashlytics|.+doubleclick.net|.+enabled-tracks|.+promoted_offer) url reject-img
^https?:\/\/api\d?\.musical\.ly\/api\/ad\/ url reject-img
# > SanLianZhongDu
https://apis.lifeweek.com.cn/api/baseConfig/getIosNewConfig url reject-200
# > ShunFeng
^https?:\/\/ccsp-egmas\.sf-express\.com\/cx-app-base\/base\/app\/appVersion\/detectionUpgrade url reject-200
https://ccsp-egmas.sf-express.com/cx-app-base/base/app/ad/queryInfoFlow url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/shunfeng_json.js
https://ccsp-egmas.sf-express.com/cx-app-base/base/app/ad/queryAdImages url reject-200
^https?:\/\/shopic\.sf-express\.com\/crm\/mobile\/common\/flashscreen url reject-200
# > shihuo
^https?:\/\/sh-gateway\.shihuo\.cn\/v\d\/services\/sh-adapi\/home\/screen url response-body egin_time":"\d{4} response-body egin_time":"2099
# > Soul
^https:\/\/data-collector\.soulapp\.cn\/api\/data\/report$ url reject-200
# > Stay
^https?:\/\/api\.shenyin\.name\/stay-fork\/browse\/featured$ url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/stay.js
# > suning
^https?:\/\/mpcs\.suning\.com\/mpcs\/dm\/getDmInfo url reject-200
# > shanmu
^https?:\/\/api-sams\.walmartmobile\.cn\/api\/v1\/sams\/sams-user\/(window\/getGoUpPlus|screen_promotion\/get) url reject-200
# > shanxianyixia
^https:\/\/api\.gameplus\.qq\.com\/community\.OnloadSrv\/GetPreloadScreenInfo url reject-200
# > shaoniandedao
^https?:\/\/igetcool-gateway\.igetcool\.com\/app-api-other-server\/white\/open\/ads.json url reject-200
# > shoulvrujia
^https?:\/\/app\.homeinns\.com\/api\/landing url reject-200
# > soutushenqi
^https?:\/\/wallpaper\.soutushenqi\.com\/v\d\/dateSignature\/random url reject-200
^https?:\/\/wallpaper\.soutushenqi\.com\/v\d\/home\/<USER>\/\/api1\.34580\.com\/wx\/Home\/AdvertisementPhotoshootRequest url reject-200
# > sanhaodongman
^https?:\/\/vidz\.3hxq\.cn\/api\/app\/(miscs\/mine\/extensions|announcements\/home) url reject-dict
# > suishenxing
^https?:\/\/m-adphone\.wenhua\.com\.cn\/ url reject-200
# > shenmezhidemai
^https?:\/\/.*zdmimg\.com\/cpm\/api\/v\d\/advert_distribution\/get_all_advertise url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/smzdm_json.js
# > shenzhouzuche
^https?:\/\/apiproxy\.zuche\.com\/resource\/cardes\/toufang\/marketing url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/dict.js


# ======= T ======= #

# > tianfutong
^https?:\/\/tft-app\.cdtft\.cn\/gateway-customer\/tftapp\/tft-ams\/api\/appAd url response-body officialAdvertResultVo response-body ddgksf2013
# > tianxingjinrong
^https?:\/\/t1\.market\.xiaomi\.com\/thumbnail\/webp\/w1170q100\/ url reject-200
# > tianyiyunpan
^https?:\/\/api\.cloud\.189\.cn\/guns\/getOpenscreenBanners url reject-200
# > tianmaojingling
^https?:\/\/zconfig\.alibabausercontent\.com\/zconfig url reject-200
# > testflight
^https?:\/\/testflight\.apple\.com\/v2\/accounts\/.*\/apps\/\d*/builds/\d*/install url request-body storefrontId" : ".*" request-body storefrontId" : "143441-1,29"
# > TengXunShouJiGuanJia
^https://otheve.beacon.qq.com\/analytics\/upload\?sid=.* url reject-200
# > TuNiu
^https?:\/\/m\.tuniu\.com\/api\/operation\/splash\/ url reject-200
# > TaoPiaoPiao
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.film\.mtopadvertiseapi\.queryadvertise\/ url reject-200
# > TianTianJiJin
^https?:\/\/appactive\.1234567\.com\.cn\/AppoperationApi\/OperationService\/GetAppStartImg url reject-200
# > topwidget
https://top-widgets-api.xiaozujian.com/api/ad/config url reject-200
# > TencentNews
^https?:\/\/r\.inews\.qq\.com\/(adsBlacklist|getFullScreenPic|getQQNewsRemoteConfig) url reject-200
^https?:\/\/r\.inews\.qq\.com\/(getBannerAds|getNewsRemoteConfig|getSplash|searchHotCatList|upLoadLoc) url reject-200
# > taipinyang
^https?:\/\/mrobot\.pconline\.com\.cn\/s-900\/onlineinfo\/cms\/launch url reject-200
# > tonghuashun
^https?:\/\/adm\.10jqka\.com\.cn\/interface\/ad\/recommend url reject-200
# > tengxundongman
^https?:\/\/iphone\.ac\.qq\.com\/.*\/Support\/(getSystemConf|bootScreen) url reject-200
# > tongchenglvxing
^https?:\/\/tcmobileapi\.17usoft\.com\/appindexnew\/index\/getappindexconfig url reject-200
# > tujiaminxu
^https?:\/\/client\.tujia\.com\/bnbapp-node\/app\/portal\/getStartPictureAdvertising url reject-200
# > tangdong
^https?:\/\/td\.cgmcare\.cn\/api\/ad url reject-200
# > taobao
^https?:\/\/guide-acs\.m\.taobao\.com\/gw\/mtop\.taobao\.wireless\.home\.splash\.awesome\.get url reject-200
# > tianmao
^https?:\/\/guide-acs\.m\.taobao\.com\/gw\/mtop\.fc\.resource\.tacdata\.get url reject-200
# > tuantuanjiayou
^https?:\/\/www\.zjyilin\.com\/hykweb\/\/index\/openImg url reject-200
# > toutiao
^https?:\/\/api-access\.[0-9a-zA-Z_-]+\.com\/api\/ad url reject-200
# > tank
^https?:\/\/gw-app-gateway\.gwmapp-w\.com\/app-api.*Type=APPSECONDAD url reject-200
# > tanghushi
^https?:\/\/api\.dnurse\.com\/api\/boot\/get-latest-items url reject-200


# ======= U ======= #


# ======= V ======= #

# > v2ex
^https?:\/\/.*v2ex\.com\/($|t\/\d+) url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/v2ex.js


# ======= W ======= #

# > weipinhui
^https?:\/\/mapi\.appvipshop\.com\/vips-mobile\/rest\/activity\/advertisement\/get url reject-200
https://b.appsimg.com/upload/momin/ url reject
https://mapi.appvipshop.com/vips-mobile/rest/activity/advertisement/get url reject-200
^https:\/\/mapi\.appvipshop\.com\/vips-mobile\/rest\/iosAdInfo\/report url reject-200
# > WangYiDaShen
^https?:\/\/god\.gameyw\.netease\.com\/v\d\/ad\/serving\/app-start url reject-200
# > WangYiyunyinyue
#^https?:\/\/interface(\d)?.music.163.com\/eapi\/ad\/ url reject-200
# > WangYiYouXiang
^https?:\/\/appconf\.mail\.163\.com\/mmad\/get\.do url reject-200
^https?:\/\/client\.mail\.163.com\/apptrack\/confinfo\/(searchMultiAds.do|showAds.do) url reject-200
# > WangYiYanXuan
https://support.you.163.com/appversync/check.do url reject-200
^https?:\/\/support\.you\.163\.com\/xhr\/boot\/getBootMedia\.json url reject-200
^https?:\/\/m\.you\.163\.com\/activity\/popWindow url reject-200
; ^https?:\/\/yanxuan.nosdn.127.net\/(static-union\/)?.*\.gif url reject-200
^https?:\/\/yanxuan\.nosdn\.127\.net\/.*\.mp4 url reject-200
# > WeChat110
^https\:\/\/(weixin110\.qq|security.wechat)\.com\/cgi-bin\/mmspamsupport-bin\/newredirectconfirmcgi\? url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/master/weixin110.js
# > weixindaihuo
^https?:\/\/mp\.weixin\.qq\.com\/mp\/cps_product_info url reject-200
# > WeChat
^https?:\/\/mp\.weixin\.qq\.com\/mp\/relatedsearchword url reject-200
^https?:\/\/mp\.weixin\.qq\.com\/mp\/getappmsgad url response-body advertisement response-body ddgksf2013
# > Weifeng
^https?:\/\/api\.wfdata\.club\/v2\/yesfeng\/(infoCenterAd|yesList) url reject-200
#^https?:\/\/api\.wfdata\.club\/v\d\/search\/hot\? url response-body dataList":\[.+\] response-body dataList":[{}]
# > Weico
^https?:\/\/overseas.weico.cc\/portal.php\?a=get_coopen_ads url reject-200
# > wangyiwoniudushu
^https?:\/\/easyreadfs\.nosdn\.127\.net\/ad-material\/ url reject-200
# > wangyikaola
^https?:\/\/sp\.kaola\.com\/api\/openad url reject-200
^https?:\/\/gw\.kaola\.com\/gw\/dgmobile\/newOpenAd url reject-200
# > wanmeishijiedianjing
^https?:\/\/api\.wmpvp\.com\/api\/v\d\/config\/promote url reject-200
# > wangxinyun
^https?:\/\/device-box\.onethingpcs\.com\/.+\/adConf url reject-200
# > wacaijizhang
^https?:\/\/jz\.wacaijizhang\.com\/api\/banners\/newSplash url reject-200
# > wodelianyungang*
^https?:\/\/file\.mylyg\.net\/banner\/(fc[a-f0-9]{30})\.jpg url reject-200


# ======= X ======= #

# > xinyue
^https?:\/\/bgw\.xinyue\.qq\.com\/xyapi\.PageService\/GetIndexPopFlash url reject-200
# > xierdun
^https?:\/\/wcprd\.hilton\.com\.cn\/app-middleware\/graphql\?type=splashAd url reject-200
# > XiaoTe
^https?:\/\/lcen\.xiaote\.net\/api\/graphql url response-body screenSplashAd response-body ddgksf2013
# > XiaoShuiMian
^https?:\/\/api.psy-1.com\/cosleep\/startup url reject-200
# > XieCheng
^https:\/\/ma-adx\.ctrip\.com\/_ma\.gif url reject-200
^https:\/\/mbd\.baidu\.com\/newspage\/api\/getmobads\?page\=landingshare url reject-200
# > XiChuangZhu
^https?:\/\/lchttpapi\.xczim\.com\/1\.1\/functions\/getLaunchImageForIOS url reject-200
# > XiaoYi
^https://api.xiaoyi.com\/v5\/app\/mobile\/ads url reject-200
^https://api.xiaoyi.com\/v5\/app\/config\?userid=.* url reject-200
# > xiachufang
^https:\/\/api\.xiachufang\.com\/v\d\/ad/ url reject-200
# > xiaolishenghuo
^https?:\/\/mpos-pic\.helipay\.com\/upload\/images\/advertisment url reject-200
# > xiaomi
^https?:\/\/api\.m\.mi\.com\/v1\/app\/start url reject-200
# > xiaomiyinxiang
^https?:\/\/marketing-aibox\.v\.mitvos\.com\/advertise\/splash url reject-200
# > xiaomiyoupin
^https?:\/\/shopapi\.io\.mi\.com\/mtop\/mf\/resource\/homePage\/pageConfig url reject-200
# > xifandongman
^https?:\/\/pzoap\.moedot\.net\/xgapp\.php\/v2\/top_notice url reject-200
# > xiangrikui
^https?:\/\/client-api\.oray\.com\/materials\/SLCC_IOS_STARTUP\?lang=zh-Hans-CN url reject-200
# > xinruijulebu
^https?:\/\/bgw\.xinyue\.qq\.com\/xyapi\.PageService\/GetIndexPopFlash url reject-200
# > xueqiu
^https?:\/\/api\.xueqiu\.com\/snowpard\/launch_strategy\/query\.json url reject-200
# > xiaoyuansouti
^https?:\/\/.*yuanfudao\.com\/iphone\/splashes url reject-200
# > xiaoyuankousuan
^https?:\/\/.*yuanfudao\.com\/leo-mis\/iphone\/splashes url reject-200
# > xiangrikui
^https?:\/\/client-api-v2\.oray\.com\/materials\/(SUNLOGIN_CLIENT_IOS_PROMOTION|SLCC_IOS_DEVICE|SLCC_IOS_STARTUP) url reject-200
# > xiaoaiyinxiang
^https?:\/\/info\.mina\.mi\.com\/advertise\/splash url reject-200
# > xiaocaibawangcan
^https?:\/\/.*1rtb\.net\/sdk\/req_ad\? url reject-200
# > xinlangcaijing
^https?:\/\/ad\.cj\.sina\.cn\/(osa\/adpreload|fax\/impress) url reject-200
# > xiaoyisheyingji
^https?:\/\/qcwx\.medproad\.com:8080\/ad\/ url reject-200
# > xiaoxiong
^https?:\/\/apps\.workair\.cn\/app\/version url response-body ads" response-body ddgksf2013"
# > xiaomishangcheng
^https?:\/\/shop-api\.retail\.mi\.com\/mtop\/navi\/skywheel\/mishop\/splash url reject-200
# > xinmanhua
^https?:\/\/xapi\.xinmanhua\.net\/splashgroups\?include=splashs url reject-200
# > xiguapi
^https?:\/\/cmt\.comp\.360os\.com\/adv url reject-200
# > xiaoqiangtingche
^https?:\/\/www\.xqpark\.cn\/xqParkApp\/resource\/getResource url reject-200
# > xiamenhangkong
^https?:\/\/mobileapi\.xiamenair\.com\/mobile-starter\/.+\/getStartAD url reject-200
# > xiaoyiguanjia
^https?:\/\/zjh5api\.ott4china\.com:8091\/cp-api\/view\/config\/pos url reject-200
# > xiaoheihe
^https?:\/\/api\.xiaoheihe\.cn\/.*get_ads_info url reject-200
# > xiaoneiwang
^https?:\/\/siteapi\.zaixs\.com\/.*\/start_ad url reject-200
# > xiaoyuzhou
^https?:\/\/api\.xiaoyuzhoufm\.com\/v\d\/(search\/get-(express|preset)|category\/list-daily-suggestion|flash-screen\/list) url reject-200
^https?:\/\/api\.xiaoyuzhoufm\.com\/v\d\/discovery-feed\/list url jsonjq-response-body '.data |= map(select(.type != "DISCOVERY_BANNER"))'


# ======= Y ======= #

# > yanaifei
^https?:\/\/pipi\.4kya\.com\/\/xgapp\.php\/v3\/advert\.position=[^2]+ url reject-200
# > YangShiPin
^https?:\/\/cdn\.cmgadx\.com\/sdk\/pool\/\w+\.json url reject-200
# > YiHaoDian
^https?:\/\/venus\.yhd\.com\/memhome\/launchConfig url reject-200
# > YiKaoBang
^https?:\/\/api\.yikaobang\.com\.cn\/client\/main\/homePageSmallAd url reject-200
^https?:\/\/api\.yikaobang\.com\.cn\/index\.php\/Client\/main\/startPage url reject-200
# > YouLinYouKe
https://new-app-api.ylyk.com/v1/user/myinfo/adviser url reject-200
# > YinkeZhiBo
^https?:\/\/service\.busi\.inke\.cn\/api\/flash\/screen url reject-200
# > YinxiangNote
^https?:\/\/app\.yinxiang\.com\/ads\/ url reject-200
# > yonghui
^https?:\/\/api\.yonghuivip\.com\/web\/shensuan\/ad\/getAd url reject-200
# > yaduo
^https?:\/\/api2\.yaduo\.com\/.*\/appLaunch url reject-200
# > youshu
^https?:\/\/gongdu\.youshu\.cc\/m\/open_screen\/list_by_udid url reject-200
# > yingshiyun
^https?:\/\/i\.ys7\.com\/api\/ads\/ url reject-200
^https?:\/\/api\.ys7\.com.*\/getAdvertising url reject-200
# > yuxueyuan
^https?:\/\/.*\.yuxueyuan\.cn\/yxy-api-gateway\/api\/json\/advert\/getsAdStartScreen url reject-200
# > yitao
^https?:\/\/acs\.m\.taobao\.com\/gw\/mtop\.etao\.noah\.query.*etao_splash url reject-200
# > yunting
^https?:\/\/ytmsout\.radio\.cn\/publish\/recScreen\/getLoadPage url reject-200
# > yangcong
^https?:\/\/m\.msyc\.cc\/app\/getBootPage\/v\d url reject-200
# > yijietu
^https?:\/\/1jietu\.com\/apiv\d\/ad url reject-200
# > youminxingkong
^https?:\/\/entry\.ubixioe\.com\/mob\/sdk\/v\d\/endpoint url reject-200
# > yihaodian
^https?:\/\/api\.m\.jd\.com\/client\.action\?functionId=home_launchConfig url reject-200
# > yimaitong
^https?:\/\/api\.medlive\.cn\/promotion-api/adlist url reject-200
^https?:\/\/promotion\.medlive\.cn\/getcover-v2\?app_name url reject-200
# > yingshiyun
^https?:\/\/api\.ys7\.com\/api\/ads url reject-200
# > youdaocidian
^https?:\/\/cdke\.youdao\.com\/course3\/recommend\/dict\/startup url reject-200
^https?:\/\/gorgon\.youdao\.com\/gorgon url reject-200
# > ycc360
^https?:\/\/ads\.closeli\.cn\/ url reject-200
# > yundaren
^https?:\/\/.*ubixioe\.com\/mob\/sdk\/v\d\/endpoint url reject-200
# > yixiaoyuan
^https?:\/\/compus\.xiaofubao\.com\/compus\/advertising url reject-200
# > yinhezhengquan
^https?:\/\/cdns\.chinastock\.com\.cn\/cdn\/omc\/app\/app\/(index_pop_banner|startup_banner) url reject-200
# > yueniuxinwen
^https?:\/\/m\.api\.shaoxing\.com\.cn\/v\d\/start url reject-200
# > yiqidazhong
^https?:\/\/oneapp-api\.faw-vw\.com\/content\/.*Code=VWAPP_ICE_OPEN_SCREEN_ADS url reject-200
# > yiqifengtian
^https?:\/\/newsuperapp\.ftms\.com\.cn\/app-bff\/.*openScreenPicture url reject-200
# > yunjing
^https:\/\/cn-app\.narwaltech\.com\/(operate\/(cactivity\/listByResourceIds|appPosition\/listSplash)|app-user-device-server\/v\d\/user-device\/getTips) url reject-200


# ======= Z ======= #

# > zhangshangshenghuo
#^https?:\/\/mlife\.cmbchina\.com\/ClientFaceService\/api\/mlife\.clientface\.clientservice\.api\.advertiseService\/preCacheAdvertiseSec url reject-200
# > zhongguoyidong
^https?:\/\/client\.app\.coc\.10086\.cn\/biz-orange\/DN\/init\/startInit url reject-200
# > zhongguoyidongyunpan
^https?:\/\/ad\.mcloud\.139\.com\/advertapi\/adv-filter\/adv-filter\/AdInfoFilter\/getAdInfos$ url reject-200
# > zhongguoyidongjiangsu
^https?:\/\/wap\.js\.10086\.cn\/jsmccClient\/cd\/market_content\/api\/v\d\/market_content\.page\.query url reject-200
# > zhongguoyidongshandong
https?:\/\/m\.sd\.10086\.cn\/zapi\/app_common\/homeWelcome\/welcome.do url reject-200
# > zhongguoliantong
^https?:\/\/m\.client\.10010\.com\/mobileService\/(activity|customer)\/(accountListData|get_client_adv|get_startadv) url reject-200
^https?:\/\/m\.client\.10010\.com\/uniAdmsInterface\/(getHomePageAd|getWelcomeAd) url reject-200
# > zhongguoyidongguangxi
^https?:\/\/gx\.10086\.cn\/zt-portal\/gxhzg\/portal\/app\/api\/v url reject-200
# > zhongguoyidonganhui*
^https?:\/\/api\.ahmobile\.cn:443\/eip\?eip_serv_id=app\.getAllNew url reject-200
# > zhuanzhuan
^https?:\/\/app\.zhuanzhuan\.com\/zzx\/transfer\/getConfigInfo url reject-200
# > zhiboba
^https?:\/\/a\.qiumibao\.com\/activities\/config\.php$ url reject-200
^https?:\/\/a\.qiumibao\.com\/ios\/config\/\?version_code= url reject-200
# > zuoyebang
^https?:\/\/syh\.zybang\.com\/com\/adx\/ url reject-200
# > ZuiYou
^https?:\/\/adapi\.izuiyou\.com\/ url reject-200
^https?:\/\/api\.izuiyou\.com\/ad\/ url reject-200
# > Zhihu
^https?:\/\/api\.zhihu\.com\/commercial_api.*launch_v2 url script-response-body https://raw.githubusercontent.com/ddgksf2013/Scripts/refs/heads/master/zhihu_openads.js
# > zhuishushenqi
^https?:\/\/adx-cn\.anythinktech\.com\/bid url reject-200
^http:\/\/[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+){1,4}(:\d+)?\/amdc\/mobileDispatch url reject-200
# > zhangshangdaojucheng
^https?:\/\/djcapp\.game\.qq\.com\/daoju\/igw\/main\/\?_service=welink\.ad\.list url reject-200
# > zhangyue
^https?:\/\/ih2\.ireader\.com\/zyapi\/bookstore\/ad url reject-200
^https?:\/\/ih2\.ireader\.com\/zyapi\/self\/screen\/ad url reject-200
^https?:\/\/ih2\.ireader\.com\/zycl\/api\/ad url reject-200
# > ziru
^https?:\/\/ztoread\.ziroom\.com\/ymerApi\/v\d\/index\/open url reject-200
# > zhangshanggongjiao
^https?:\/\/quanguo\.mygolbs\.com:8081\/MyBusServer\/servlet\/MyGoServer\.HttpPool\.HttpHandlerServlet url reject-200
# > zhongguoyinhang
#^https?:\/\/mbs\.boc\.cn\/ubas-mgateway-static\/images\/advertType\/.+.jpg url reject-img
# > zhongguotianqi
^https?:\/\/[a-zA-Z0-9_-]+\.admobile.top\/ url reject-200
^https?:\/\/[a-zA-Z0-9_-]+\.ranfenghd\.com\/ad url reject-200
^https?:\/\/[a-zA-Z0-9_-]+\.tianmu\.mobi\/ad url reject-200
^https?:\/\/[a-zA-Z0-9_-]+\.weathercn\.com\/(.*bypage|appindexicon) url reject-200
# > zhuijudaren
^https?:\/\/zjdr666\.com\/zjdr\.php\/v\d\/(version|top_notice\?|advert\?position=[^2]+) url reject-200
# > zhangshangyingxionglianmeng
^https?:\/\/mlol\.qt\.qq\.com\/go\/recommend\/(?!v) url reject-200
# > zhongguoyidongguangdong
^https?:\/\/gd\.10086\.cn\/gmccapp\/serv\/\?servicename=GMCCAPP_704_002_001_001 url reject-200
# > zhixinghuochepiao
^https?:\/\/m\.ctrip\.com\/restapi\/soa2\/\d+\/scjson\/tripAds url reject-200
# > zijinnongshangyinhang*
^https?:\/\/zjmbank\.js96008\.com:8090\/gw\/advert\/oprAdvertQry url reject-200
# > zhangyu
^https?:\/\/.*zhangyuyidong\.cn\/api\/ url reject-200
# > zhongguoguangdian
^https?:\/\/app\.10099\.com\.cn\/contact-web\/api\/version\/getFlashScreenPage url reject-200
# > zhihuisuzhou
^https?:\/\/newapp2\.szsmk\.com\/app\/config\/queryMainAd url reject-200
# > zhangzhougonggongjiaotong
^https?:\/\/app\.ibuscloud\.com\/v\d\/(app\/getSkipAdvert|notice\/getNoticeWithAdvByCity) url reject-200
# > zhangyue
^https?:\/\/api\.ireader\.mobi\/activity\/ad\/openScreen url reject-200
# > zhangyue
^https?:\/\/api\.ireaderm\.net\/activity\/(ad|popup\/info) url reject-200
# > zhaojin
^https?:\/\/i\.zhaojinapp\.com\/APPAD url reject-200
# > zonghengxiaoshuo
^https?:\/\/.*zongheng\.com\/iosapi\/system\/startup url reject-200
# > zcool
^https?:\/\/api\.zcool\.com\.cn\/.*common\/open-screen url reject-200



hostname = api.xiaoyuzhoufm.com, beijing-gateway-customer.app-prod.bjev.com.cn, **************, api2.yaduo.com, ***************, **************, *************, ************, api.ireaderm.net, marketing-aibox.v.mitvos.com, ad.seeyouyima.com, data.hgold.cn, *ziben.com, *.zhijianmedia.cn, prod.zh.igpsport.com, apptec.leapmotor.cn, admin-app.edifier.com, ac1.dcloud.net.cn, *.weathercn.com, *.tianmu.mobi, *.ranfenghd.com, tiebac.baidu.com, adx.zuoyebang.com, www.hpoi.net.cn, cdn.web.chelaile.net.cn, www.ctm.net, yyfapi.huatu.com, pjlapi.paojd.cn, api.dnurse.com, sdk.tianmu.mobi, newsuperapp.ftms.com.cn, gw-app-gateway.gwmapp-w.com, *.zongheng.com, oneapp-api.faw-vw.com, creditcardapp.bankcomm.com, api.369cx.cn, api.xiaoheihe.cn, api.zcool.com.cn, api.livelab.com.cn, pinggai*.caixin.com, *.5eplay.com, app.duxiaoman.com, *.flyert.*, lop-proxy.jd.com, ***************, i.zhaojinapp.com, patientgate.91160.com, iyes.youku.com, gateway.cotticoffee.com, app.jiantou8.com, capi.lkcoffee.com, www.123pan.com, api-access.pangolin-sdk-toutiao.*com, zjh5api.ott4china.com, napi.ithome.com, api.qd-metro.com, api.kurobbs.com, apiproxy.zuche.com, api.smart.jd.com, mobileapi.xiamenair.com, home.mi.com, apps.teamair.cn, cn-app.narwaltech.com, api2.picooc.com, file.mylyg.net, lens.leoao.com, www.ahzs10000.com, api.qbb6.com, *.zdmimg.com, cdns.chinastock.com.cn, api.futunn.com, api.ireader.mobi, www.xqpark.cn, api.jk.cn, api.moomoo.com, api.ys7.com, cq11344-app-https-api-1.ictun.com, hxqapi.hiyun.tv, m-adphone.wenhua.com.cn, *.fenbi.com, api.medlive.cn, smsrebuild1.mail.10086.cn, ad.mcloud.139.com, comapi.reader.qq.com, commontgw.reader.qq.com, fuwu.nhsa.gov.cn, compus.xiaofubao.com, *.ubixioe.com, bp-api.bestv.com.cn, app.ibuscloud.com, www.kujiale.com, mama.dxy.com, newapp2.szsmk.com, vidz.3hxq.cn, xapi.xinmanhua.net, jz.wacaijizhang.com, ads.closeli.cn, shop-api.retail.mi.com, gorgon.youdao.com, cdke.youdao.com, m.stock.pingan.com, *.1rtb.net, careapi.oclean.com, app.10099.com.cn, hc-ssp.sm.cn, apps.workair.cn, app-api.medsci.cn, ad.cj.sina.cn, app-cdn.2q10.com, api.merach.com, advertise.bczeducation.cn, notify.baicizhan.com, app.api.qjjfin.com, dynamicad.kfc.com.cn, ump.sz.creditcard.ecitic.com, *.didapinche.com, api.mcd.cn, comicapi.manhuashe.com, acs-m.freshippo.com, zjmbank.js96008.com, www.flyert.com, api.ys7.com, saad.ms.zhangyue.net, dj.palmestore.com, device-box.onethingpcs.com, *.anythinktech.com, api.xueqiu.com, **************, ***************, switch.jumpvg.com, mobilepaas.abchina.com.cn, api-one-wscn.awtmt.com, restapi.iyunmai.com, maicai.api.ddxq.mobi, info.mina.mi.com, qadx.qinlinad.com, app-gateway.leisuapi.com, app.c.nf.migu.cn, app.flymodem.com.cn, hfapp-service.qweather.net, *.ahhhhfs.com, promotion.medlive.cn, mobads.baidu.com, entree-ws.igetget.com, axxd.xmseeyouyima.com, guide-acs.m.taobao.com, *.mting.info, ad.lofter.com, entry.ubixioe.com, *.v2ex.com, adv.ccb.com, client-api-v2.oray.com, www.pansearch.me, td.cgmcare.cn, *.yuanfudao.com, ad.mcloud.139.com, wallpaper.soutushenqi.com, **************, ap.dongqiudi.com, apapia-sqk.manmanbuy.com, *.tipatipa.xyz, superapp.xgimi.com, mix-api.camera360.com, api1.34580.com, api.wmpvp.com, gateway.abite.com, rengine-platform.llsapp.com, 1jietu.com, bbs-api.miyoushe.com, tcmobileapi.17usoft.com, m.msyc.cc, mgw.mpaas.cn-hangzhou.aliyuncs.com, ytmsout.radio.cn, api.kmovie.gifshow.com, igetcool-gateway.igetcool.com, *.xbwpys.com, open-cms-api.quark.cn, ftapi.10jqka.com.cn, wxa.wxs.qq.com, evs.500.com, j1.pupuapi.com, dq.dxy.cn, m.you.163.com, open-cms-api.uc.cn, *.yuxueyuan.cn, pan-api.bitqiu.com, client.tujia.com, vip7.fzwdyy.cn, *.qyfxgd.cn, *.weilai555.com, *.ecoliving168.com, *cupid.iqiyi.com, gateway.36kr.com, shopapi.io.mi.com, tk.lanjiyin.com, gongdu.youshu.cc, api.21ec74.com, ztoread.ziroom.com, iphone.ac.qq.com, web.chelaile.net.cn, gd.10086.cn, api.00bang.cn, app.hbooker.com, api.sfacg.com, newclient.map.baidu.com, api3.cls.cn, gateway-api.dushu365.com, p0.pipi.cn, jdread-api.jd.com, ms.jr.jd.com, bdsp-x.jd.com, dsp-x.jd.com, api.m.jd.com, router-app-api.jdcloud.com, app.homeinns.com, cdn-evone-ceph.echargenet.com, gg.caixin.com, app-gw.csdn.net, api.gameplus.qq.com, mrobot.pconline.com.cn, djcapp.game.qq.com, mxsa.mxbc.net, cn-acs.m.cainiao.com, zjdr666.com, api.caiyunapp.com, api.ahmobile.cn, gx.10086.cn, emdcad*.eastmoney.com, api-sams.walmartmobile.cn, creditcardapp.bankcomm.cn, lban.spdb.com.cn, adapi.izuiyou.com, i.ys7.com, apps.api.ke.com, api.yonghuivip.com, access.mypikpak.com, gateway.shouqiev.com, res.pizzahut.com.cn, api.caijingmobile.com, 3g.csair.com, client-api.oray.com, bgw.xinyue.qq.com, api-new.app.acfun.cn, m.client.10010.com, open.fitdays.cn, gha.ghac.cn, sh-gateway.shihuo.cn, wcprd.hilton.com, ad.12306.cn, **************, adx-cn.anythinktech.com, js-ad.ayximgs.com, api.dangdang.com, pzoap.moedot.com, pipi.4kya.com, vip7.fzwdyy.cn, sdk.alibaba.com.ailbaba.me, tft-app.cdtft.cn, bgw.xinyue.qq.com, pan.baidu.com, yunbusiness.ccb.com, client.app.coc.10086.cn, t1.market.xiaomi.com, api.m.mi.com, app.zhuanzhuan.com, api.cloud.189.cn, appwk.baidu.com, us.l.qq.com, open.e.kuaishou.com, mi.gdt.qq.com, adm.10jqka.com.cn, a.qiumibao.com, api.tipsoon.com, b.appsimg.com, ios.sspai.com, zconfig.alibabausercontent.com, api.touker.com, mpcs.suning.com, api.yangkeduo.com, easyreadfs.nosdn.127.net, sp.kaola.com, gw.kaola.com, mime.baidu.com, learn.chaoxing.com, mapi.dangdang.com, api.dangdang.com, open.e.kuaishou.com, client.qunar.com, mpos-pic.helipay.com, dili.bdatu.com, wap.ngchina.cn, ih2.ireader.com, adpai.thepaper.cn, api.haohaozhu.cn, list-app-m.i4.cn, api.xiachufang.com, peisongapi.meituan.com, *gaoqingdianshi.com, *.kakamobi.cn, api.jxedt.com, slapi.oray.net, r.inews.qq.com, app.yinxiang.com, mapi.appvipshop.com, syh.zybang.com, cdn.cmgadx.com, api.internetofcity.cn, img.meituan.net, lcen.xiaote.net, venus.yhd.com, api.shenyin.name, mage*.if.qidian.com, god.gameyw.netease.com, m.ibuscloud.com, e.dangdang.com, app2.autoimg.cn, adproxy.autohome.com.cn, *.umetrip.com, explorer.tratao.com, overseas.weico.cc, ***************, **************, 120.241.*, dapis.mting.info, acs.m.taobao.com, open3.vistastory.com, ssp.dzh.com.cn, www.meituan.com, client.mail.163.com, api.izuiyou.com, appapi.huazhu.com, api.wfdata.club, interface*.music.163.com, api.psy-1.com, res.kfc.com.cn, security.wechat.com, weixin110.qq.com, cdn.*.chelaileapp.cn, api.coolapk.com, app3.qdaily.com, daoyu.sdo.com, img.jiemian.com, ccsp-egmas.sf-express.com, *.laichon.com, app.ap.d3yuiw4.com, www.i3zh.com, api.yikaobang.com.cn, api-ad-product.huxiu.com, jad-api.jin10.com, mob.mddcloud.com.cn, appactive.1234567.com.cn, rtbapi.douyucdn.cn, service.busi.inke.cn, gab.122.gov.cn, dispatcher.camera360.com, app.api.versa-ai.com, open.taou.com, api.taou.com, track.mm.taou.com, app.dewu.com, data-collector.soulapp.cn, api.gamer.com.tw, beta-api.crunchyroll.com, api.vistopia.com.cn, pss.txffp.com, m.sd.10086.cn, helper.2bulu.com, *************, api.weibo.cn, mapi.weibo.com, *.uve.weibo.com, new.vip.weibo.cn, wap.js.10086.cn, tagit.hyhuo.com, top-widgets-api.xiaozujian.com, *mangaapi.manhuaren.*, apis.lifeweek.com.cn, support.you.163.com, yanxuan.nosdn.127.net, *.peopleapp.com, new-app-api.ylyk.com, gw.aihuishou.com, *.58cdn.com.cn, app.58.com, api.zhihu.com, creditcardapp.bankcomm.com, res.xiaojukeji.com, ***********, ***********, **************, lchttpapi.xczim.com, business.msstatic.com, api.kkmh.com, *.chelaile.net.cn, api.jr.mi.com, api.m.mi.com, guanyu.longfor.com, elemecdn.com, fuss10.elemecdn.com, www1.elecfans.com, m.tuniu.com, mapi.mafengwo.cn, api.xiaoyi.com, api.douban.com, otheve.beacon.qq.com, mapi.sfbest.com, api.mwee.cn, sso.ifanr.com, s3plus.meituan.net, flowplus.meituan.net, p*.meituan.net, testflight.apple.com, wmapi.meituan.com, appconf.mail.163.com, dl-cu-hz.lechange.cn, fbchina.flipchina.cn, zone.guiderank-app.com, homefront.qunar.com, afd.baidu.com, ma-adx.ctrip.com, mbd.baidu.com, img.rr.tv, api.rr.tv, *.hoopchina.com, goblin.hupu.com, spclient.wg.spotify.com
