name: Clear Commits

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4.2.2
        with:
          ref: master
      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git checkout --orphan clearhistory
          git branch -D master
          git checkout --orphan master
          git add .
          git commit -m "ㅤ"
          git push -f -u origin master
      - name: Cleanup Workflow
        uses: Mattraks/delete-workflow-runs@main
        with:
          retain_days: 0
          keep_minimum_runs: 0