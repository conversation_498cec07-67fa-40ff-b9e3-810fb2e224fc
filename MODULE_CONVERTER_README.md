# 代理软件模块转换系统

这个系统可以自动将不同代理软件的重写模块进行相互转换，支持 Loon、QuantumultX、Surge 之间的转换。

## 功能特性

- 🔄 **自动转换**: 支持 Loon、QX、Surge 模块之间的相互转换
- ⏰ **定时执行**: 每天凌晨2点自动执行转换任务
- 📁 **统一管理**: 转换后的文件统一保存为 `.conf` 格式
- 🗂️ **分类存储**: 按目标类型分目录存储转换结果
- 🔧 **易于维护**: 通过配置文件管理模块源，方便增删
- 🌟 **智能默认**: 不指定目标类型时，自动转换为所有可用类型

## 目录结构

```
Tool/
├── module_sources.json          # 模块源配置文件
├── convert_modules.py           # 转换脚本
├── .github/workflows/
│   └── convert-modules.yml      # GitHub Action 工作流
└── module/                      # 转换结果目录
    ├── surge/                   # Surge 模块
    ├── loon/                    # Loon 模块
    ├── stash/                   # Stash 模块
    └── shadowrocket/            # Shadowrocket 模块
```

## 支持的转换类型

| 源类型 | 目标类型 |
|--------|----------|
| Loon | Surge, Stash, Shadowrocket |
| QuantumultX | Surge, Loon, Stash, Shadowrocket |
| Surge | Loon, Stash, Shadowrocket |

## 配置文件说明

### module_sources.json

这个文件包含了所有需要转换的模块源信息：

```json
{
  "loon": [
    {
      "name": "模块名称",
      "url": "模块原始地址",
      "targets": ["surge", "stash", "shadowrocket"]
    },
    {
      "name": "不指定目标的模块",
      "url": "模块原始地址"
      // 不指定targets时，默认转换所有可用类型
    }
  ],
  "qx": [
    {
      "name": "模块名称",
      "url": "模块原始地址",
      "targets": ["surge", "loon", "stash", "shadowrocket"]
    }
  ],
  "surge": [
    {
      "name": "模块名称",
      "url": "模块原始地址",
      "targets": ["loon", "stash", "shadowrocket"]
    }
  ]
}
```

## 如何添加新模块

1. 编辑 `module_sources.json` 文件
2. 在对应的源类型数组中添加新的模块配置
3. 提交更改到仓库，GitHub Action 会自动触发转换

### 目标类型配置说明

- **指定目标类型**: 在模块配置中添加 `"targets"` 字段，指定要转换的目标类型
- **默认转换全部**: 不添加 `"targets"` 字段，系统会自动转换为所有可用的目标类型

```json
{
  "loon": [
    {
      "name": "指定目标的模块",
      "url": "https://example.com/module.lpx",
      "targets": ["surge", "stash"]
    },
    {
      "name": "默认转换全部的模块",
      "url": "https://example.com/module2.lpx"
      // 会自动转换为: surge, loon, stash, shadowrocket
    }
  ]
}
```

## 手动执行转换

### 本地执行

```bash
python convert_modules.py
```

### GitHub Action 手动触发

1. 进入仓库的 Actions 页面
2. 选择 "Convert Proxy Modules" 工作流
3. 点击 "Run workflow" 按钮

## 转换工具

使用自部署的 script-hub 项目进行转换：
- 基础URL: https://sc.sephiroth.club/
- 支持多种代理软件格式之间的转换
- 自动处理语法差异和兼容性问题

## 注意事项

1. **文件格式**: 所有转换后的文件统一使用 `.conf` 后缀
2. **目录清理**: 每次转换前会清空原有的模块目录
3. **错误处理**: 转换失败的模块会在日志中显示错误信息
4. **网络依赖**: 需要网络连接来下载原始模块和转换服务

## 故障排除

### 转换失败
- 检查原始模块URL是否可访问
- 确认转换服务 (sc.sephiroth.club) 是否正常
- 查看 GitHub Action 日志获取详细错误信息

### 文件未更新
- 检查模块源是否有实际变化
- 确认 GitHub Action 有足够的权限提交更改

## 更新日志

转换结果会自动提交到仓库，提交信息格式：
```
🔄 Auto update proxy modules - YYYY-MM-DD HH:MM:SS
```
